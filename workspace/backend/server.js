import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import helmet from 'helmet';
import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Database setup
const db = new sqlite3.Database(path.join(__dirname, 'database.sqlite'));

// Initialize database tables
db.serialize(() => {
  // Contact form table
  db.run(`CREATE TABLE IF NOT EXISTS contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    reason TEXT NOT NULL,
    message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // User activity table
  db.run(`CREATE TABLE IF NOT EXISTS user_activity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    page_path TEXT NOT NULL,
    user_agent TEXT,
    ip_address TEXT,
    referrer TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);
});

// Middleware to track page visits
const trackPageVisit = (req, res, next) => {
  const pageMap = {
    '/': 'home',
    '/services': 'services',
    '/about': 'about',
    '/contact': 'contact'
  };

  const page = req.headers.referer ? new URL(req.headers.referer).pathname : '/';
  const pageName = pageMap[page] || 'unknown';

  db.run(
    `INSERT INTO user_activity (page_path, user_agent, ip_address, referrer) 
     VALUES (?, ?, ?, ?)`,
    [pageName, req.get('User-Agent'), req.ip, req.get('Referer')]
  );

  next();
};

// Routes

// Track page visits
app.post('/api/track', (req, res) => {
  const { page, userAgent, timestamp } = req.body;
  
  db.run(
    `INSERT INTO user_activity (page_path, user_agent, ip_address, referrer) 
     VALUES (?, ?, ?, ?)`,
    [page, userAgent, req.ip, req.get('Referer')],
    function(err) {
      if (err) {
        console.error('Error tracking page visit:', err);
        return res.status(500).json({ error: 'Failed to track visit' });
      }
      res.json({ success: true, id: this.lastID });
    }
  );
});

// Contact form submission
app.post('/api/contact', (req, res) => {
  const { name, email, phone, reason, message } = req.body;

  if (!name || !email || !reason) {
    return res.status(400).json({ error: 'Name, email, and reason are required' });
  }

  db.run(
    `INSERT INTO contacts (name, email, phone, reason, message) 
     VALUES (?, ?, ?, ?, ?)`,
    [name, email, phone, reason, message],
    function(err) {
      if (err) {
        console.error('Error saving contact:', err);
        return res.status(500).json({ error: 'Failed to save contact information' });
      }
      res.json({ 
        success: true, 
        message: 'Contact information saved successfully',
        id: this.lastID 
      });
    }
  );
});

// Get analytics data
app.get('/api/analytics', (req, res) => {
  db.all(
    `SELECT page_path, COUNT(*) as visits, 
     DATE(timestamp) as date
     FROM user_activity 
     GROUP BY page_path, DATE(timestamp)
     ORDER BY timestamp DESC`,
    (err, rows) => {
      if (err) {
        console.error('Error fetching analytics:', err);
        return res.status(500).json({ error: 'Failed to fetch analytics' });
      }
      res.json(rows);
    }
  );
});

// Get page visit counts
app.get('/api/analytics/summary', (req, res) => {
  db.all(
    `SELECT page_path, COUNT(*) as total_visits
     FROM user_activity 
     GROUP BY page_path
     ORDER BY total_visits DESC`,
    (err, rows) => {
      if (err) {
        console.error('Error fetching analytics summary:', err);
        return res.status(500).json({ error: 'Failed to fetch analytics summary' });
      }
      res.json(rows);
    }
  );
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});