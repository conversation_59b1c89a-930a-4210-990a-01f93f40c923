import React, { useState } from 'react'
import axios from 'axios'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    reason: '',
    message: ''
  })
  const [status, setStatus] = useState({ type: '', message: '' })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setStatus({ type: '', message: '' })

    try {
      const response = await axios.post('/api/contact', formData)
      setStatus({
        type: 'success',
        message: 'Thank you for your message! We\'ll get back to you within 24 hours.'
      })
      setFormData({
        name: '',
        email: '',
        phone: '',
        reason: '',
        message: ''
      })
    } catch (error) {
      setStatus({
        type: 'error',
        message: 'Sorry, there was an error sending your message. Please try again.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div>
      <section className="section">
        <h1 style={{ textAlign: 'center', marginBottom: '2rem', fontSize: '3rem' }}>
          Contact Us
        </h1>
        <p style={{ textAlign: 'center', fontSize: '1.2rem', color: '#666', maxWidth: '600px', margin: '0 auto 3rem' }}>
          Ready to transform your business with AI? Get in touch with our experts to discuss your needs and discover the perfect solution for your organization.
        </p>

        <div style={{ maxWidth: '800px', margin: '0 auto', display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '3rem' }}>
          {/* Contact Information */}
          <div>
            <h2 style={{ marginBottom: '2rem', color: '#333' }}>Get in Touch</h2>
            
            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{ color: '#667eea', marginBottom: '1rem' }}>📧 Email Us</h3>
              <p><EMAIL></p>
              <p><EMAIL></p>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{ color: '#667eea', marginBottom: '1rem' }}>📞 Call Us</h3>
              <p>+1 (555) 123-4567</p>
              <p>Monday - Friday: 9:00 AM - 6:00 PM EST</p>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{ color: '#667eea', marginBottom: '1rem' }}>🏢 Visit Us</h3>
              <p>123 AI Innovation Drive<br />
              Tech City, TC 12345<br />
              United States</p>
            </div>

            <div>
              <h3 style={{ color: '#667eea', marginBottom: '1rem' }}>⚡ Quick Response</h3>
              <p>We typically respond to all inquiries within 24 hours. For urgent matters, please call us directly.</p>
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <form onSubmit={handleSubmit} className="contact-form">
              {status.message && (
                <div className={`message ${status.type}`}>
                  {status.message}
                </div>
              )}

              <div className="form-group">
                <label htmlFor="name">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Enter your full name"
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email Address *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  placeholder="Enter your email address"
                />
              </div>

              <div className="form-group">
                <label htmlFor="phone">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter your phone number"
                />
              </div>

              <div className="form-group">
                <label htmlFor="reason">Reason for Contact *</label>
                <select
                  id="reason"
                  name="reason"
                  value={formData.reason}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select a reason</option>
                  <option value="general_inquiry">General Inquiry</option>
                  <option value="consultation_request">Consultation Request</option>
                  <option value="technical_support">Technical Support</option>
                  <option value="partnership">Partnership Opportunity</option>
                  <option value="pricing">Pricing Information</option>
                  <option value="demo_request">Request a Demo</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Tell us more about your needs or questions..."
                  rows="5"
                />
              </div>

              <button
                type="submit"
                className="submit-button"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </button>
            </form>
          </div>
        </div>

        {/* FAQ Section */}
        <div style={{ marginTop: '4rem', maxWidth: '800px', margin: '4rem auto 0' }}>
          <h2 style={{ textAlign: 'center', marginBottom: '2rem' }}>Frequently Asked Questions</h2>
          <div className="services-grid">
            <div className="service-card">
              <h3>How quickly can you implement AI solutions?</h3>
              <p>Implementation timelines vary based on complexity, but most of our solutions can be deployed within 2-8 weeks.</p>
            </div>
            <div className="service-card">
              <h3>Do you offer ongoing support?</h3>
              <p>Yes, we provide comprehensive support packages including monitoring, maintenance, and continuous optimization.</p>
            </div>
            <div className="service-card">
              <h3>Can your solutions integrate with existing systems?</h3>
              <p>Absolutely! Our solutions are designed to seamlessly integrate with your current technology stack.</p>
            </div>
            <div className="service-card">
              <h3>What industries do you serve?</h3>
              <p>We work across various industries including healthcare, finance, retail, manufacturing, and professional services.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Contact