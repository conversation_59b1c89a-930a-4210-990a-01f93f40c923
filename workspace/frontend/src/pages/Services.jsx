import React from 'react'
import { Link } from 'react-router-dom'

const Services = () => {
  const services = [
    {
      title: "Conversational AI Agents",
      description: "Deploy intelligent chatbots and virtual assistants that understand natural language and provide personalized customer support 24/7.",
      features: ["Natural Language Processing", "Multi-language Support", "Integration Ready", "Learning Capabilities"]
    },
    {
      title: "Process Automation",
      description: "Automate repetitive tasks and complex workflows with AI-powered solutions that adapt to your business processes.",
      features: ["Workflow Automation", "Document Processing", "Data Analysis", "Decision Making"]
    },
    {
      title: "Predictive Analytics",
      description: "Leverage machine learning algorithms to predict trends, customer behavior, and business outcomes with high accuracy.",
      features: ["Trend Forecasting", "Customer Insights", "Risk Assessment", "Performance Optimization"]
    },
    {
      title: "Computer Vision Solutions",
      description: "Extract insights from images and videos with advanced computer vision technology for quality control and analysis.",
      features: ["Image Recognition", "Object Detection", "Quality Control", "Real-time Processing"]
    },
    {
      title: "AI Consulting & Strategy",
      description: "Get expert guidance on AI implementation strategy, technology selection, and digital transformation roadmaps.",
      features: ["Strategy Development", "Technology Assessment", "Implementation Planning", "Training & Support"]
    },
    {
      title: "Custom AI Development",
      description: "Build tailored AI solutions designed specifically for your unique business requirements and industry challenges.",
      features: ["Custom Models", "API Development", "Cloud Deployment", "Ongoing Maintenance"]
    }
  ]

  return (
    <div>
      <section className="section">
        <h1 style={{ textAlign: 'center', marginBottom: '2rem', fontSize: '3rem' }}>
          Our AI Services
        </h1>
        <p style={{ textAlign: 'center', fontSize: '1.2rem', color: '#666', maxWidth: '800px', margin: '0 auto 3rem' }}>
          Comprehensive AI solutions designed to transform your business operations, 
          enhance customer experiences, and drive sustainable growth through intelligent automation.
        </p>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <h3>{service.title}</h3>
              <p style={{ marginBottom: '1.5rem' }}>{service.description}</p>
              <h4 style={{ color: '#667eea', marginBottom: '1rem' }}>Key Features:</h4>
              <ul style={{ color: '#666', paddingLeft: '1.2rem' }}>
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} style={{ marginBottom: '0.5rem' }}>{feature}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div style={{ textAlign: 'center', marginTop: '3rem', padding: '2rem', background: '#f8f9fa', borderRadius: '15px' }}>
          <h2>Ready to Get Started?</h2>
          <p style={{ fontSize: '1.1rem', color: '#666', marginBottom: '2rem' }}>
            Contact our AI experts to discuss your specific needs and discover how our solutions can benefit your business.
          </p>
          <Link to="/contact" className="cta-button">
            Schedule a Consultation
          </Link>
        </div>
      </section>
    </div>
  )
}

export default Services