import React from 'react'
import { Link } from 'react-router-dom'

const About = () => {
  return (
    <div>
      <section className="section">
        <h1 style={{ textAlign: 'center', marginBottom: '2rem', fontSize: '3rem' }}>
          About AI Solutions
        </h1>
        
        <div style={{ maxWidth: '800px', margin: '0 auto', lineHeight: '1.8', fontSize: '1.1rem' }}>
          <p style={{ marginBottom: '2rem', textAlign: 'center', fontSize: '1.3rem', color: '#667eea' }}>
            Pioneering the future of business through intelligent AI solutions
          </p>
          
          <h2 style={{ color: '#333', marginTop: '3rem', marginBottom: '1rem' }}>Our Mission</h2>
          <p style={{ marginBottom: '2rem' }}>
            At AI Solutions, we believe that artificial intelligence should be accessible, practical, and transformative 
            for businesses of all sizes. Our mission is to democratize AI technology by providing cutting-edge solutions 
            that are easy to implement, cost-effective, and deliver measurable results.
          </p>

          <h2 style={{ color: '#333', marginBottom: '1rem' }}>Our Story</h2>
          <p style={{ marginBottom: '2rem' }}>
            Founded in 2023 by a team of AI researchers and industry veterans, AI Solutions emerged from the recognition 
            that many businesses struggle to harness the power of artificial intelligence. We set out to bridge this gap 
            by creating user-friendly AI agents and automation tools that solve real business problems.
          </p>

          <h2 style={{ color: '#333', marginBottom: '1rem' }}>What Sets Us Apart</h2>
          <div className="services-grid" style={{ marginTop: '2rem' }}>
            <div className="service-card">
              <h3>🎯 Business-First Approach</h3>
              <p>
                We don't just build AI for the sake of technology. Every solution is designed with clear business 
                objectives and measurable outcomes in mind.
              </p>
            </div>
            <div className="service-card">
              <h3>🧠 Expert Team</h3>
              <p>
                Our team combines deep technical expertise in machine learning with extensive business experience 
                across various industries.
              </p>
            </div>
            <div className="service-card">
              <h3>⚡ Rapid Deployment</h3>
              <p>
                Our solutions are designed for quick implementation with minimal disruption to your existing 
                business processes.
              </p>
            </div>
            <div className="service-card">
              <h3>🤝 Partnership Focus</h3>
              <p>
                We work closely with our clients as long-term partners, ensuring continuous improvement and 
                adaptation to evolving needs.
              </p>
            </div>
          </div>

          <h2 style={{ color: '#333', marginTop: '3rem', marginBottom: '1rem' }}>Our Expertise</h2>
          <p style={{ marginBottom: '1rem' }}>
            With deep expertise in machine learning, natural language processing, and computer vision, we deliver 
            AI solutions across multiple domains:
          </p>
          <ul style={{ marginBottom: '2rem', paddingLeft: '2rem' }}>
            <li>Customer Service Automation</li>
            <li>Predictive Analytics and Forecasting</li>
            <li>Process Optimization and Automation</li>
            <li>Intelligent Document Processing</li>
            <li>Computer Vision Applications</li>
            <li>Natural Language Understanding</li>
          </ul>

          <h2 style={{ color: '#333', marginBottom: '1rem' }}>Our Values</h2>
          <div style={{ background: '#f8f9fa', padding: '2rem', borderRadius: '15px', marginBottom: '2rem' }}>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
              <div>
                <h4 style={{ color: '#667eea', marginBottom: '0.5rem' }}>Innovation</h4>
                <p>Continuously pushing the boundaries of what's possible with AI technology.</p>
              </div>
              <div>
                <h4 style={{ color: '#667eea', marginBottom: '0.5rem' }}>Integrity</h4>
                <p>Building trust through transparent communication and ethical AI practices.</p>
              </div>
              <div>
                <h4 style={{ color: '#667eea', marginBottom: '0.5rem' }}>Excellence</h4>
                <p>Delivering high-quality solutions that exceed expectations and drive results.</p>
              </div>
              <div>
                <h4 style={{ color: '#667eea', marginBottom: '0.5rem' }}>Collaboration</h4>
                <p>Working as true partners with our clients to achieve shared success.</p>
              </div>
            </div>
          </div>

          <div style={{ textAlign: 'center', marginTop: '3rem' }}>
            <h2>Ready to Transform Your Business?</h2>
            <p style={{ marginBottom: '2rem', color: '#666' }}>
              Let's discuss how our AI solutions can help you achieve your business goals.
            </p>
            <Link to="/contact" className="cta-button">
              Start Your AI Journey
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About